{% extends "base.html" %}

{% block title %}Error {{ status_code }} - IP Lookup{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <div class="mb-8">
            <h1 class="text-6xl font-bold text-dark-accent mb-4">{{ status_code }}</h1>
            <h2 class="text-2xl font-semibold text-dark-text mb-2">{{ error }}</h2>
            <p class="text-dark-text/70">
                {% if status_code == 404 %}
                    The page you're looking for doesn't exist.
                {% elif status_code == 500 %}
                    Something went wrong on our end.
                {% else %}
                    An unexpected error occurred.
                {% endif %}
            </p>
        </div>
        
        <div class="space-y-4">
            <a href="/" class="inline-block bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200">
                Go Home
            </a>
            
            {% if status_code != 404 %}
            <div>
                <button onclick="window.history.back()" class="text-dark-accent hover:text-dark-secondary transition-colors duration-200">
                    ← Go Back
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

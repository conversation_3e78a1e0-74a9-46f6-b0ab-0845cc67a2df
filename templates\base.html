<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}IP Lookup Service{% endblock %}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            bg: '#121212',
                            card: '#1E1E1E',
                            input: '#282828',
                            text: '#E0E0E0',
                            accent: '#1ABC9C',
                            secondary: '#2ECC71'
                        }
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            padding: 16px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .toast.success {
            background-color: #10B981;
        }
        
        .toast.error {
            background-color: #EF4444;
        }
        
        .spinner {
            border: 2px solid #282828;
            border-top: 2px solid #1ABC9C;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-dark-bg text-dark-text min-h-screen">
    <!-- Toast container -->
    <div id="toast-container"></div>
    
    {% block content %}{% endblock %}
    
    <script>
        // Toast notification system
        function showToast(message, type = 'success') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);
            
            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => container.removeChild(toast), 300);
            }, 5000);
        }
        
        // Loading state management
        function setLoading(element, loading = true) {
            if (loading) {
                element.classList.add('loading');
                const spinner = element.querySelector('.spinner');
                if (spinner) spinner.style.display = 'inline-block';
            } else {
                element.classList.remove('loading');
                const spinner = element.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';
            }
        }
        
        // API helper functions
        async function apiCall(url, options = {}) {
            const token = localStorage.getItem('access_token');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token && !options.skipAuth) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });
                
                if (response.status === 401 && !options.skipAuth) {
                    // Token expired, redirect to login
                    localStorage.removeItem('access_token');
                    window.location.href = '/login';
                    return;
                }
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || 'Request failed');
                }
                
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }
        
        // Check authentication status
        function isAuthenticated() {
            return localStorage.getItem('access_token') !== null;
        }
        
        // Logout function
        function logout() {
            localStorage.removeItem('access_token');
            window.location.href = '/';
        }
        
        // Format date helper
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }
        
        // Escape HTML helper
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>

#!/usr/bin/env python3
"""
Setup script for IP Lookup application.
This script initializes the database and creates an admin user.
"""

import asyncio
import os
import sys
from getpass import getpass

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import create_db_and_tables, startup_database, shutdown_database
from auth import create_admin_user, validate_password_strength


async def setup_database():
    """Initialize the database with tables and indexes."""
    print("Setting up database...")
    try:
        await create_db_and_tables()
        await startup_database()
        print("✓ Database setup completed successfully")
        return True
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False
    finally:
        try:
            await shutdown_database()
        except:
            pass


async def create_initial_admin():
    """Create the initial admin user."""
    print("\nCreating admin user...")
    
    # Get username
    while True:
        username = input("Enter admin username (4-50 characters): ").strip()
        if len(username) >= 4 and len(username) <= 50:
            break
        print("Username must be between 4 and 50 characters")
    
    # Get password
    while True:
        password = getpass("Enter admin password: ")
        confirm_password = getpass("Confirm admin password: ")
        
        if password != confirm_password:
            print("Passwords do not match. Please try again.")
            continue
        
        if not validate_password_strength(password):
            print("Password must be at least 8 characters and include:")
            print("- At least one uppercase letter")
            print("- At least one lowercase letter") 
            print("- At least one digit")
            print("- At least one special character")
            continue
        
        break
    
    try:
        await startup_database()
        user_id = await create_admin_user(username, password)
        print(f"✓ Admin user '{username}' created successfully (ID: {user_id})")
        return True
    except ValueError as e:
        print(f"✗ Failed to create admin user: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error creating admin user: {e}")
        return False
    finally:
        try:
            await shutdown_database()
        except:
            pass


async def main():
    """Main setup function."""
    print("IP Lookup Application Setup")
    print("=" * 30)
    
    # Setup database
    if not await setup_database():
        print("\nSetup failed. Please check the error messages above.")
        sys.exit(1)
    
    # Create admin user
    create_admin = input("\nDo you want to create an admin user? (y/n): ").lower().strip()
    if create_admin in ['y', 'yes']:
        if not await create_initial_admin():
            print("\nAdmin user creation failed, but database setup was successful.")
            print("You can create an admin user later using the auth module.")
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nTo start the application:")
    print("1. Copy .env.example to .env and configure your settings")
    print("2. Run: uvicorn main:app --reload")
    print("3. Open http://localhost:8000 in your browser")
    print("\nFor production deployment:")
    print("1. Set proper environment variables")
    print("2. Use a production WSGI server like Gunicorn")
    print("3. Set up a reverse proxy like NGINX")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nSetup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during setup: {e}")
        sys.exit(1)

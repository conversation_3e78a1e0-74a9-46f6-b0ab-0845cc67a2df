"""
Comprehensive logging configuration for IP Lookup web application.
Provides structured logging for different components and activities.
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


def setup_logging():
    """Configure comprehensive logging for the application."""
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s | %(name)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # 1. Application Activity Log (General application events)
    app_handler = logging.handlers.RotatingFileHandler(
        log_dir / "app_activity.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    app_handler.setLevel(logging.INFO)
    app_handler.setFormatter(detailed_formatter)
    
    # 2. Security Log (Authentication, authorization, security events)
    security_handler = logging.handlers.RotatingFileHandler(
        log_dir / "security.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,  # Keep more security logs
        encoding='utf-8'
    )
    security_handler.setLevel(logging.INFO)
    security_handler.setFormatter(detailed_formatter)
    
    # 3. API Access Log (HTTP requests and responses)
    api_handler = logging.handlers.RotatingFileHandler(
        log_dir / "api_access.log",
        maxBytes=20*1024*1024,  # 20MB
        backupCount=7,
        encoding='utf-8'
    )
    api_handler.setLevel(logging.INFO)
    api_handler.setFormatter(simple_formatter)
    
    # 4. Database Activity Log (Database operations)
    db_handler = logging.handlers.RotatingFileHandler(
        log_dir / "database.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    db_handler.setLevel(logging.INFO)
    db_handler.setFormatter(detailed_formatter)
    
    # 5. Error Log (Errors and exceptions)
    error_handler = logging.handlers.RotatingFileHandler(
        log_dir / "errors.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # 6. Admin Activity Log (Admin actions and operations)
    admin_handler = logging.handlers.RotatingFileHandler(
        log_dir / "admin_activity.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    admin_handler.setLevel(logging.INFO)
    admin_handler.setFormatter(detailed_formatter)
    
    # Console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    # Add handlers to root logger
    root_logger.addHandler(app_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    
    # Security logger
    security_logger = logging.getLogger('security')
    security_logger.addHandler(security_handler)
    security_logger.propagate = False  # Don't propagate to root
    
    # API logger
    api_logger = logging.getLogger('api')
    api_logger.addHandler(api_handler)
    api_logger.propagate = False
    
    # Database logger
    db_logger = logging.getLogger('database')
    db_logger.addHandler(db_handler)
    db_logger.propagate = False
    
    # Admin logger
    admin_logger = logging.getLogger('admin')
    admin_logger.addHandler(admin_handler)
    admin_logger.propagate = False
    
    # Suppress some noisy third-party loggers
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    logging.getLogger('passlib').setLevel(logging.WARNING)
    
    return {
        'app': logging.getLogger('app'),
        'security': security_logger,
        'api': api_logger,
        'database': db_logger,
        'admin': admin_logger,
        'error': logging.getLogger('error')
    }


def log_request(request, response_status: int, response_time: float = None):
    """Log HTTP request details."""
    api_logger = logging.getLogger('api')
    
    client_ip = getattr(request.client, 'host', 'unknown')
    method = request.method
    url = str(request.url)
    user_agent = request.headers.get('user-agent', 'unknown')
    
    log_msg = f"{client_ip} | {method} {url} | Status: {response_status}"
    if response_time:
        log_msg += f" | Time: {response_time:.3f}s"
    if user_agent != 'unknown':
        log_msg += f" | UA: {user_agent[:100]}"
    
    api_logger.info(log_msg)


def log_security_event(event_type: str, username: str = None, ip_address: str = None, details: str = None):
    """Log security-related events."""
    security_logger = logging.getLogger('security')
    
    log_msg = f"Security Event: {event_type}"
    if username:
        log_msg += f" | User: {username}"
    if ip_address:
        log_msg += f" | IP: {ip_address}"
    if details:
        log_msg += f" | Details: {details}"
    
    security_logger.info(log_msg)


def log_admin_action(action: str, username: str, ip_address: str = None, details: dict = None):
    """Log admin actions."""
    admin_logger = logging.getLogger('admin')
    
    log_msg = f"Admin Action: {action} | User: {username}"
    if ip_address:
        log_msg += f" | IP: {ip_address}"
    if details:
        log_msg += f" | Details: {details}"
    
    admin_logger.info(log_msg)


def log_database_operation(operation: str, table: str = None, record_count: int = None, details: str = None):
    """Log database operations."""
    db_logger = logging.getLogger('database')
    
    log_msg = f"DB Operation: {operation}"
    if table:
        log_msg += f" | Table: {table}"
    if record_count is not None:
        log_msg += f" | Records: {record_count}"
    if details:
        log_msg += f" | Details: {details}"
    
    db_logger.info(log_msg)


def log_application_event(event: str, details: str = None):
    """Log general application events."""
    app_logger = logging.getLogger('app')
    
    log_msg = f"App Event: {event}"
    if details:
        log_msg += f" | Details: {details}"
    
    app_logger.info(log_msg)


def log_error(error: Exception, context: str = None, user: str = None):
    """Log errors with context."""
    error_logger = logging.getLogger('error')
    
    log_msg = f"Error: {type(error).__name__}: {str(error)}"
    if context:
        log_msg += f" | Context: {context}"
    if user:
        log_msg += f" | User: {user}"
    
    error_logger.error(log_msg, exc_info=True)


# Initialize logging when module is imported
loggers = setup_logging()

# Export commonly used loggers
app_logger = loggers['app']
security_logger = loggers['security']
api_logger = loggers['api']
database_logger = loggers['database']
admin_logger = loggers['admin']
error_logger = loggers['error']

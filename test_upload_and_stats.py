#!/usr/bin/env python3
"""
Test script to upload sample data and verify dashboard statistics.
"""

import requests
import json
import time

def test_upload_and_stats():
    """Test uploading data and checking dashboard stats."""
    base_url = "http://localhost:8000"
    
    print("Testing Upload and Dashboard Statistics")
    print("=" * 50)
    
    try:
        # Step 1: Try to login with common credentials
        print("\n1. Attempting to login...")
        
        # Try different login combinations
        login_attempts = [
            {"username": "admin", "password": "admin123"},
            {"username": "admin", "password": "SecurePassword123!"},
            {"username": "simjiale", "password": "password123"},
            {"username": "testadmin", "password": "TestPass123!"}
        ]
        
        token = None
        for creds in login_attempts:
            try:
                response = requests.post(
                    f"{base_url}/api/v1/auth/login/",
                    json=creds,
                    timeout=5
                )
                if response.status_code == 200:
                    data = response.json()
                    token = data.get("access_token")
                    print(f"   ✓ Login successful with {creds['username']}")
                    break
                else:
                    print(f"   ✗ Login failed for {creds['username']}: {response.status_code}")
            except Exception as e:
                print(f"   ✗ Login error for {creds['username']}: {e}")
        
        if not token:
            print("   ✗ Could not login with any credentials")
            return False
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Step 2: Test the stats endpoint
        print("\n2. Testing dashboard stats endpoint...")
        
        try:
            response = requests.get(
                f"{base_url}/api/v1/admin/stats/",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                stats = response.json()
                print(f"   ✓ Stats endpoint successful")
                print(f"   Total Records: {stats.get('total_records', 'N/A')}")
                print(f"   Last Upload: {stats.get('last_upload', 'N/A')}")
                print(f"   Active Admins: {stats.get('active_admins', 'N/A')}")
            else:
                print(f"   ✗ Stats endpoint failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ✗ Stats endpoint error: {e}")
            return False
        
        # Step 3: Upload a single record
        print("\n3. Testing single record upload...")
        
        try:
            upload_data = {
                "ip": "*************",
                "country": "Test Country",
                "owner": "Test Owner",
                "comment": "Test upload for dashboard stats"
            }
            
            response = requests.post(
                f"{base_url}/api/v1/admin/upload/",
                headers=headers,
                data=upload_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✓ Single upload successful")
                print(f"   Inserted: {result.get('inserted', 0)}")
                print(f"   Errors: {len(result.get('errors', []))}")
            else:
                print(f"   ✗ Single upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ✗ Single upload error: {e}")
        
        # Step 4: Check stats again
        print("\n4. Checking updated stats...")
        
        try:
            # Wait a moment for the database to update
            time.sleep(1)
            
            response = requests.get(
                f"{base_url}/api/v1/admin/stats/",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                new_stats = response.json()
                print(f"   ✓ Updated stats retrieved")
                print(f"   Total Records: {new_stats.get('total_records', 'N/A')}")
                print(f"   Last Upload: {new_stats.get('last_upload', 'N/A')}")
                print(f"   Active Admins: {new_stats.get('active_admins', 'N/A')}")
                
                # Compare with previous stats
                if new_stats.get('total_records', 0) > stats.get('total_records', 0):
                    print("   ✓ Total records increased after upload")
                
                if new_stats.get('last_upload') != stats.get('last_upload'):
                    print("   ✓ Last upload time updated")
                    
            else:
                print(f"   ✗ Updated stats failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ✗ Updated stats error: {e}")
        
        print("\n" + "=" * 50)
        print("✓ Upload and stats test completed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = test_upload_and_stats()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        exit(1)

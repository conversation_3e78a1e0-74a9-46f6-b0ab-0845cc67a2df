import os
import sqlite3
from sqlmodel import SQLModel, create_engine
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./ip_lookup.db")
DB_FILE = DATABASE_URL.replace("sqlite:///", "./")

# Create engines
engine = create_engine(DATABASE_URL, echo=False)


async def create_db_and_tables():
    """Create database tables if they don't exist."""
    try:
        # Import models to ensure they're registered
        from models import IPRecord, AdminUser, AuditLog, LoginAttempt

        # Create tables using sync engine for initial setup
        SQLModel.metadata.create_all(engine)

        # Wait a moment for tables to be created
        import time
        time.sleep(0.1)

        # Create indexes for better performance
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()

            # Check if tables exist first
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"Created tables: {tables}")

            if 'ip_records' in tables:
                # Create composite index for IP range lookups
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_ip_range
                    ON ip_records(ip_start, ip_end)
                """)

                # Create additional indexes for filtering
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_country
                    ON ip_records(country)
                """)

                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_owner
                    ON ip_records(owner)
                """)

                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_added_at
                    ON ip_records(added_at)
                """)

            if 'login_attempts' in tables:
                # Index for login attempts
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_login_attempts_username_time
                    ON login_attempts(username, timestamp)
                """)

            if 'audit_logs' in tables:
                # Index for audit logs
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_audit_admin_time
                    ON audit_logs(admin_id, timestamp)
                """)

            conn.commit()

        logger.info("Database tables and indexes created successfully")

    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


async def check_database_health():
    """Check if database is accessible and healthy."""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


# Database startup and shutdown handlers
async def startup_database():
    """Connect to database on startup."""
    try:
        # Just check if database file exists and is accessible
        await check_database_health()
        logger.info("Database connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise


async def shutdown_database():
    """Disconnect from database on shutdown."""
    try:
        logger.info("Database disconnected successfully")
    except Exception as e:
        logger.error(f"Error disconnecting from database: {e}")


# Utility functions for database operations
async def execute_query(query: str, values: dict = None) -> List[Dict[str, Any]]:
    """Execute a query and return results."""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            if values:
                cursor.execute(query, values)
            else:
                cursor.execute(query)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    except Exception as e:
        logger.error(f"Query execution failed: {query}, Error: {e}")
        raise


async def execute_one(query: str, values: dict = None) -> Optional[Dict[str, Any]]:
    """Execute a query and return single result."""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            if values:
                cursor.execute(query, values)
            else:
                cursor.execute(query)
            row = cursor.fetchone()
            return dict(row) if row else None
    except Exception as e:
        logger.error(f"Query execution failed: {query}, Error: {e}")
        raise


async def execute_insert(query: str, values: dict) -> int:
    """Execute an insert query and return the last row id."""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            return cursor.lastrowid
    except Exception as e:
        logger.error(f"Insert execution failed: {query}, Error: {e}")
        raise


# Transaction support
class DatabaseTransaction:
    """Context manager for database transactions."""

    def __init__(self):
        self.conn = None

    async def __aenter__(self):
        self.conn = sqlite3.connect(DB_FILE)
        self.conn.row_factory = sqlite3.Row
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.conn.rollback()
        else:
            self.conn.commit()
        self.conn.close()

    async def execute(self, query: str, values: dict = None):
        """Execute query within transaction."""
        cursor = self.conn.cursor()
        if values:
            cursor.execute(query, values)
        else:
            cursor.execute(query)
        return cursor.lastrowid

    async def fetch_one(self, query: str, values: dict = None):
        """Fetch one result within transaction."""
        cursor = self.conn.cursor()
        if values:
            cursor.execute(query, values)
        else:
            cursor.execute(query)
        row = cursor.fetchone()
        return dict(row) if row else None


# Initialize database on module import
async def init_database():
    """Initialize database with tables and indexes."""
    await create_db_and_tables()
    await startup_database()

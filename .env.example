# Database Configuration
DATABASE_URL=sqlite:///./ip_lookup.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:8000,http://127.0.0.1:8000

# Application Environment
API_ENV=development

# Admin User (for initial setup)
# Create admin user with: python -c "from auth import create_admin_user; import asyncio; asyncio.run(create_admin_user('admin', 'SecurePassword123!'))"
ADMIN_USERNAME=admin
ADMIN_PASSWORD=SecurePassword123!

#!/usr/bin/env python3
"""
Test script to verify dashboard statistics functionality.
"""

import asyncio
import sqlite3
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import startup_database, shutdown_database, execute_one
from auth import create_admin_user, authenticate_user
import json

async def test_dashboard_stats():
    """Test the dashboard statistics functionality."""
    print("Testing Dashboard Statistics Functionality")
    print("=" * 50)
    
    try:
        await startup_database()
        
        # Test 1: Check current database state
        print("\n1. Current Database State:")
        
        # Count IP records
        count_result = await execute_one("SELECT COUNT(*) as total FROM ip_records", {})
        total_records = count_result["total"] if count_result else 0
        print(f"   Total IP Records: {total_records}")
        
        # Check audit logs
        audit_result = await execute_one("SELECT COUNT(*) as total FROM audit_logs", {})
        total_audits = audit_result["total"] if audit_result else 0
        print(f"   Total Audit Logs: {total_audits}")
        
        # Check last upload
        last_upload_query = """
            SELECT MAX(timestamp) as last_upload, action_type, detail_json
            FROM audit_logs
            WHERE action_type IN ('bulk_upload', 'single_upload')
        """
        last_upload_result = await execute_one(last_upload_query, {})
        if last_upload_result and last_upload_result["last_upload"]:
            print(f"   Last Upload: {last_upload_result['last_upload']}")
            print(f"   Upload Type: {last_upload_result['action_type']}")
            try:
                details = json.loads(last_upload_result["detail_json"])
                print(f"   Upload Details: {details}")
            except:
                print(f"   Upload Details: {last_upload_result['detail_json']}")
        else:
            print("   Last Upload: Never")
        
        # Test 2: Test the stats endpoint logic
        print("\n2. Testing Stats Endpoint Logic:")
        
        # Get total IP records count
        count_query = "SELECT COUNT(*) as total FROM ip_records"
        count_result = await execute_one(count_query, {})
        total_records = count_result["total"] if count_result else 0
        print(f"   Total Records Query Result: {total_records}")
        
        # Get last upload time from audit logs
        last_upload_query = """
            SELECT MAX(timestamp) as last_upload
            FROM audit_logs
            WHERE action_type IN ('bulk_upload', 'single_upload')
            AND detail_json NOT LIKE '%"inserted": 0%'
        """
        last_upload_result = await execute_one(last_upload_query, {})
        last_upload = last_upload_result["last_upload"] if last_upload_result else None
        print(f"   Last Upload Query Result: {last_upload}")
        
        # Get active admin count
        active_admins_query = """
            SELECT COUNT(DISTINCT admin_id) as active_count
            FROM audit_logs 
            WHERE action_type = 'login'
            AND timestamp >= datetime('now', '-30 days')
        """
        active_admins_result = await execute_one(active_admins_query, {})
        active_admins = active_admins_result["active_count"] if active_admins_result else 1
        print(f"   Active Admins Query Result: {active_admins}")
        
        # Test 3: Simulate the API response
        print("\n3. Simulated API Response:")
        api_response = {
            "total_records": total_records,
            "last_upload": last_upload.isoformat() if last_upload else None,
            "active_admins": active_admins
        }
        print(f"   API Response: {json.dumps(api_response, indent=2)}")
        
        print("\n" + "=" * 50)
        print("✓ Dashboard statistics test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            await shutdown_database()
        except:
            pass

if __name__ == "__main__":
    try:
        success = asyncio.run(test_dashboard_stats())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)

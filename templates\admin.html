{% extends "base.html" %}

{% block title %}Admin Dashboard - IP Lookup{% endblock %}

{% block content %}
<div class="min-h-screen bg-dark-bg">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 w-64 bg-dark-card shadow-lg">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-dark-bg">
                <h1 class="text-xl font-bold text-dark-accent">IP-Check Admin</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2">
                <a href="/admin" class="nav-link active flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="#" id="export-link" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export IPs
                </a>
                
                <a href="/admin/upload" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Upload IPs
                </a>
                
                <a href="/admin/search" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search/Filter
                </a>
            </nav>
            
            <!-- Logout -->
            <div class="px-4 py-4 border-t border-gray-600">
                <button id="logout-btn" class="w-full flex items-center px-4 py-2 text-red-400 rounded-lg hover:bg-red-900/20 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Logout
                </button>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="ml-64 p-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-dark-text">Dashboard</h1>
            <p class="text-dark-text/70 mt-2">Overview of your IP database</p>
        </div>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <div class="flex items-center">
                    <div class="p-2 bg-dark-accent/20 rounded-lg">
                        <svg class="w-6 h-6 text-dark-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-dark-text/70">Total IP Records</p>
                        <p id="total-records" class="text-2xl font-bold text-dark-text">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <div class="flex items-center">
                    <div class="p-2 bg-dark-secondary/20 rounded-lg">
                        <svg class="w-6 h-6 text-dark-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-dark-text/70">Last Upload</p>
                        <p id="last-upload" class="text-2xl font-bold text-dark-text">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-500/20 rounded-lg">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-dark-text/70">Active Admins</p>
                        <p id="active-admins" class="text-2xl font-bold text-dark-text">Loading...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
            <h2 class="text-xl font-semibold text-dark-text mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button id="quick-export" class="p-4 bg-dark-input rounded-lg hover:bg-gray-600 transition-colors duration-200 text-left">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 text-dark-accent mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <h3 class="font-medium text-dark-text">Download CSV</h3>
                            <p class="text-sm text-dark-text/70">Export all IP records</p>
                        </div>
                    </div>
                </button>
                
                <a href="/admin/upload" class="p-4 bg-dark-input rounded-lg hover:bg-gray-600 transition-colors duration-200 text-left block">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 text-dark-secondary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <div>
                            <h3 class="font-medium text-dark-text">Upload Records</h3>
                            <p class="text-sm text-dark-text/70">Add new IP entries</p>
                        </div>
                    </div>
                </a>
                
                <a href="/admin/search" class="p-4 bg-dark-input rounded-lg hover:bg-gray-600 transition-colors duration-200 text-left block">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-medium text-dark-text">Search Database</h3>
                            <p class="text-sm text-dark-text/70">Filter and query records</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return;
    }
    
    // Load dashboard data
    loadDashboardStats();
    
    // Event listeners
    document.getElementById('logout-btn').addEventListener('click', logout);
    document.getElementById('export-link').addEventListener('click', handleExport);
    document.getElementById('quick-export').addEventListener('click', handleExport);
    
    async function loadDashboardStats() {
        try {
            // For now, we'll use placeholder data since we don't have specific stats endpoints
            // In a real implementation, you'd create dedicated endpoints for dashboard stats
            
            // Simulate loading delay
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Set placeholder values (in production, fetch from API)
            document.getElementById('total-records').textContent = '0';
            document.getElementById('last-upload').textContent = 'Never';
            document.getElementById('active-admins').textContent = '1';
            
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
            showToast('Failed to load dashboard statistics', 'error');
        }
    }
    
    async function handleExport(e) {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/v1/admin/export/', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            if (!response.ok) {
                throw new Error('Export failed');
            }
            
            // Create download link
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ip_records_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showToast('Export completed successfully', 'success');
            
        } catch (error) {
            console.error('Export failed:', error);
            showToast('Export failed', 'error');
        }
    }
});
</script>
{% endblock %}

2025-06-01 12:58:57 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:58:57 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:58:57 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:58:57 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:58:57 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:58:57 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:10 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:10 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:11 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:11 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:11 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:11 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:11 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:11 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:20 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:20 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:21 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:21 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:21 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:21 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:21 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:21 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:29 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:29 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:30 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:30 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:30 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:30 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:30 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:30 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:40 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:40 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:41 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:41 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:41 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:41 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:41 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:41 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:52 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:52 | database | INFO | shutdown_database:112 | Database disconnected successfully
2025-06-01 12:59:53 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:53 | database | INFO | create_db_and_tables:37 | Created tables: ['ip_records', 'admin_users', 'audit_logs', 'login_attempts']
2025-06-01 12:59:53 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:53 | database | INFO | create_db_and_tables:78 | Database tables and indexes created successfully
2025-06-01 12:59:53 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 12:59:53 | database | INFO | startup_database:103 | Database connected successfully
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER
2025-06-01 13:01:37 | database | ERROR | execute_one:148 | Query execution failed: 
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                , Error: Python int too large to convert to SQLite INTEGER

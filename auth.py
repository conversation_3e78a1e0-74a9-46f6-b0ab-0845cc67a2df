import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from database import execute_one, execute_insert, execute_query
import logging

logger = logging.getLogger(__name__)

# Security configuration
SECRET_KEY = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def validate_password_strength(password: str) -> bool:
    """Validate password meets complexity requirements."""
    if len(password) < 8:
        return False
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_symbol = any(not c.isalnum() for c in password)
    
    return has_upper and has_lower and has_digit and has_symbol


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict):
    """Create a JWT refresh token."""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_user_by_username(username: str):
    """Get user from database by username."""
    query = "SELECT * FROM admin_users WHERE username = :username AND is_active = true"
    return await execute_one(query, {"username": username})


async def authenticate_user(username: str, password: str, client_ip: str):
    """Authenticate user and handle login attempts."""
    # Check for account lockout
    if await is_account_locked(username):
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked due to too many failed attempts"
        )
    
    user = await get_user_by_username(username)
    
    # Record login attempt
    success = False
    if user and verify_password(password, user["hashed_pwd"]):
        success = True
        # Update last login time
        update_query = """
            UPDATE admin_users 
            SET last_login_at = :timestamp 
            WHERE id = :user_id
        """
        await execute_insert(update_query, {
            "timestamp": datetime.utcnow(),
            "user_id": user["id"]
        })
    
    # Log the attempt
    await log_login_attempt(username, client_ip, success)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )
    
    return user


async def log_login_attempt(username: str, ip_address: str, success: bool):
    """Log a login attempt to the database."""
    query = """
        INSERT INTO login_attempts (username, ip_address, timestamp, success)
        VALUES (:username, :ip_address, :timestamp, :success)
    """
    await execute_insert(query, {
        "username": username,
        "ip_address": ip_address,
        "timestamp": datetime.utcnow(),
        "success": success
    })


async def is_account_locked(username: str) -> bool:
    """Check if account is locked due to failed login attempts."""
    # Check failed attempts in last 10 minutes
    ten_minutes_ago = datetime.utcnow() - timedelta(minutes=10)
    
    query = """
        SELECT COUNT(*) as failed_count
        FROM login_attempts 
        WHERE username = :username 
        AND timestamp > :since 
        AND success = false
    """
    
    result = await execute_one(query, {
        "username": username,
        "since": ten_minutes_ago
    })
    
    failed_count = result["failed_count"] if result else 0
    return failed_count >= 5


async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token and return user info."""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        # Check if token is expired
        exp = payload.get("exp")
        if exp is None or datetime.utcnow() > datetime.fromtimestamp(exp):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        
        return {"username": username, "user_id": payload.get("user_id")}
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


async def get_current_user(token_data: dict = Depends(verify_token)):
    """Get current authenticated user."""
    user = await get_user_by_username(token_data["username"])
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    return user


async def log_admin_action(admin_id: int, action_type: str, details: dict):
    """Log admin action to audit log."""
    query = """
        INSERT INTO audit_logs (admin_id, action_type, detail_json, timestamp)
        VALUES (:admin_id, :action_type, :detail_json, :timestamp)
    """
    
    await execute_insert(query, {
        "admin_id": admin_id,
        "action_type": action_type,
        "detail_json": json.dumps(details),
        "timestamp": datetime.utcnow()
    })


async def create_admin_user(username: str, password: str):
    """Create a new admin user (for initial setup)."""
    if not validate_password_strength(password):
        raise ValueError("Password does not meet complexity requirements")
    
    # Check if username already exists
    existing_user = await get_user_by_username(username)
    if existing_user:
        raise ValueError("Username already exists")
    
    hashed_password = get_password_hash(password)
    
    query = """
        INSERT INTO admin_users (username, hashed_pwd, is_active, created_at)
        VALUES (:username, :hashed_pwd, :is_active, :created_at)
    """
    
    user_id = await execute_insert(query, {
        "username": username,
        "hashed_pwd": hashed_password,
        "is_active": True,
        "created_at": datetime.utcnow()
    })
    
    logger.info(f"Admin user created: {username}")
    return user_id


# Dependency for protected routes
async def require_admin(current_user: dict = Depends(get_current_user)):
    """Dependency that requires admin authentication."""
    if not current_user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is not active"
        )
    return current_user

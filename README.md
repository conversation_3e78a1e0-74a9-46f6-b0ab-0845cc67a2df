# IP Lookup Web Application

A minimalist, dark-themed IP lookup web application built with FastAPI and SQLite. This application allows users to lookup IP addresses and provides an admin interface for managing IP records.

## Features

### Public Features
- **IP Lookup**: Search for IPv4, IPv6, or CIDR ranges
- **Dark Theme**: Modern, minimalist dark interface
- **Fast Response**: Optimized database queries with proper indexing

### Admin Features
- **Secure Authentication**: JWT-based admin authentication
- **IP Management**: Add single records or bulk upload via CSV
- **Export Data**: Download all records as CSV
- **Search & Filter**: Advanced filtering with pagination
- **Audit Logging**: Track all admin actions

## Technology Stack

- **Backend**: FastAPI (Python 3.10+)
- **Database**: SQLite with async support
- **ORM**: SQLModel for type-safe database operations
- **Authentication**: JWT tokens with bcrypt password hashing
- **Frontend**: Vanilla HTML/CSS/JavaScript with Tailwind CSS
- **Deployment**: Docker support included

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ip-lookup-app

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
```

### 2. Database Setup

```bash
# Run the setup script
python setup.py
```

This will:
- Create the database tables and indexes
- Optionally create an admin user

### 3. Start the Application

```bash
# Development mode
uvicorn main:app --reload

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000
```

The application will be available at `http://localhost:8000`

### 4. Docker Deployment

```bash
# Build the image
docker build -t ip-lookup .

# Run the container
docker run -p 8000:8000 -v $(pwd)/data:/app/data ip-lookup
```

## Usage

### Public IP Lookup

1. Visit the homepage at `http://localhost:8000`
2. Enter an IP address or CIDR range (e.g., `*******` or `***********/24`)
3. Click "Lookup" to see results

### Admin Interface

1. Visit `http://localhost:8000/login`
2. Log in with your admin credentials
3. Access the admin dashboard at `http://localhost:8000/admin`

#### Admin Functions

- **Dashboard**: View statistics and quick actions
- **Upload IPs**: Add single records or bulk upload CSV files
- **Search/Filter**: Query records with advanced filtering
- **Export**: Download all records as CSV

#### CSV Upload Format

For bulk uploads, use this CSV format:
```
ip_cidr,country,owner,comment
***********/24,United States,Example Corp,Corporate network
*******,United States,Google LLC,Public DNS
```

## API Documentation

The API documentation is automatically generated and available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Key Endpoints

#### Public Endpoints
- `GET /health-check/` - Health check
- `POST /api/v1/lookup/` - IP lookup

#### Authentication
- `POST /api/v1/auth/login/` - Admin login

#### Admin Endpoints (require authentication)
- `GET /api/v1/admin/export/` - Export all records
- `POST /api/v1/admin/upload/` - Upload records
- `POST /api/v1/admin/query/` - Search/filter records

## Configuration

### Environment Variables

Create a `.env` file with these settings:

```bash
# Database
DATABASE_URL=sqlite:///./ip_lookup.db

# JWT Secret (change in production!)
JWT_SECRET=your-super-secret-jwt-key

# CORS
ALLOWED_ORIGINS=http://localhost:8000

# Environment
API_ENV=development
```

### Security Considerations

1. **Change JWT Secret**: Use a strong, unique secret in production
2. **HTTPS**: Always use HTTPS in production
3. **Database Permissions**: Restrict database file permissions
4. **Rate Limiting**: Built-in rate limiting for public endpoints
5. **Password Policy**: Strong password requirements enforced

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
python -m pytest test_basic.py -v
```

### Project Structure

```
├── main.py              # FastAPI application
├── models.py            # Database models
├── database.py          # Database configuration
├── auth.py              # Authentication logic
├── api/                 # API endpoints
│   ├── lookup.py        # Public lookup endpoint
│   ├── auth.py          # Authentication endpoints
│   └── admin.py         # Admin endpoints
├── templates/           # HTML templates
├── static/              # Static files (CSS, JS)
├── requirements.txt     # Python dependencies
├── Dockerfile          # Docker configuration
└── setup.py            # Setup script
```

### Adding New Features

1. **Database Changes**: Update models in `models.py`
2. **API Endpoints**: Add new routes in the `api/` directory
3. **Frontend**: Update templates and static files
4. **Tests**: Add tests in `test_basic.py` or create new test files

## Performance

### Database Optimization

- Composite indexes on IP ranges for fast lookups
- Streaming CSV export to handle large datasets
- Pagination for search results

### Expected Performance

- Single IP lookup: < 50ms
- CIDR range queries: < 200ms
- Bulk upload: Processes thousands of records efficiently

## Troubleshooting

### Common Issues

1. **Database locked**: Ensure only one instance is running
2. **Permission denied**: Check file permissions on database
3. **Import errors**: Verify all dependencies are installed
4. **Port in use**: Change port in uvicorn command

### Logs

Check application logs for detailed error information:
```bash
# View logs in development
uvicorn main:app --log-level debug

# In production, configure proper logging
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Create an issue in the repository

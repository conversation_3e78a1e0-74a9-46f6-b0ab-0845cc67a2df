#!/usr/bin/env python3
"""
Basic tests for IP Lookup application.
Run with: python -m pytest test_basic.py -v
"""

import pytest
import asyncio
from httpx import AsyncClient
from main import app
from models import IPRecord
import ipaddress


class TestIPRecord:
    """Test the IPRecord model."""
    
    def test_ipv4_single_ip(self):
        """Test creating IPRecord from single IPv4."""
        record = IPRecord.from_cidr("***********", "United States", "Test Corp", "Test comment")
        
        assert record.ip_cidr == "***********/32"
        assert record.ip_version == 4
        assert record.ip_start == int(ipaddress.ip_address("***********"))
        assert record.ip_end == int(ipaddress.ip_address("***********"))
        assert record.country == "United States"
        assert record.owner == "Test Corp"
        assert record.comment == "Test comment"
    
    def test_ipv4_cidr(self):
        """Test creating IPRecord from IPv4 CIDR."""
        record = IPRecord.from_cidr("***********/24", "United States", "Test Corp")
        
        assert record.ip_cidr == "***********/24"
        assert record.ip_version == 4
        assert record.ip_start == int(ipaddress.ip_address("***********"))
        assert record.ip_end == int(ipaddress.ip_address("*************"))
        assert record.comment is None
    
    def test_ipv6_single_ip(self):
        """Test creating IPRecord from single IPv6."""
        record = IPRecord.from_cidr("2001:db8::1", "United States", "Test Corp")
        
        assert record.ip_cidr == "2001:db8::1/128"
        assert record.ip_version == 6
        assert record.ip_start == int(ipaddress.ip_address("2001:db8::1"))
        assert record.ip_end == int(ipaddress.ip_address("2001:db8::1"))
    
    def test_ipv6_cidr(self):
        """Test creating IPRecord from IPv6 CIDR."""
        record = IPRecord.from_cidr("2001:db8::/64", "United States", "Test Corp")
        
        assert record.ip_cidr == "2001:db8::/64"
        assert record.ip_version == 6
        network = ipaddress.ip_network("2001:db8::/64")
        assert record.ip_start == int(network.network_address)
        assert record.ip_end == int(network.broadcast_address)
    
    def test_invalid_ip(self):
        """Test that invalid IP raises ValueError."""
        with pytest.raises(ValueError):
            IPRecord.from_cidr("invalid.ip", "United States", "Test Corp")


class TestAPI:
    """Test API endpoints."""
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/health-check/")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_lookup_invalid_ip(self):
        """Test lookup with invalid IP."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/api/v1/lookup/", json={"query": "invalid.ip"})
            assert response.status_code == 400
            data = response.json()
            assert "Invalid IP/CIDR format" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_lookup_not_found(self):
        """Test lookup with valid IP that doesn't exist in database."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/api/v1/lookup/", json={"query": "*******"})
            assert response.status_code == 404
            data = response.json()
            assert "No data found" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/api/v1/auth/login/", json={
                "username": "nonexistent",
                "password": "wrongpassword"
            })
            assert response.status_code == 401
            data = response.json()
            assert "Invalid username or password" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_admin_endpoints_require_auth(self):
        """Test that admin endpoints require authentication."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Test export endpoint
            response = await client.get("/api/v1/admin/export/")
            assert response.status_code == 403  # No Authorization header
            
            # Test upload endpoint
            response = await client.post("/api/v1/admin/upload/", json={
                "ip": "***********/24",
                "country": "Test",
                "owner": "Test Corp"
            })
            assert response.status_code == 403  # No Authorization header
            
            # Test query endpoint
            response = await client.post("/api/v1/admin/query/", json={
                "filters": [],
                "pagination": {"skip": 0, "limit": 10}
            })
            assert response.status_code == 403  # No Authorization header


def test_password_validation():
    """Test password validation function."""
    from auth import validate_password_strength
    
    # Valid passwords
    assert validate_password_strength("SecurePass123!")
    assert validate_password_strength("MyP@ssw0rd")
    
    # Invalid passwords
    assert not validate_password_strength("short")  # Too short
    assert not validate_password_strength("nouppercase123!")  # No uppercase
    assert not validate_password_strength("NOLOWERCASE123!")  # No lowercase
    assert not validate_password_strength("NoDigits!")  # No digits
    assert not validate_password_strength("NoSymbols123")  # No symbols


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

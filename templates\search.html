{% extends "base.html" %}

{% block title %}Search IPs - Admin Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-dark-bg">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 w-64 bg-dark-card shadow-lg">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-dark-bg">
                <h1 class="text-xl font-bold text-dark-accent">IP-Check Admin</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2">
                <a href="/admin" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="#" id="export-link" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export IPs
                </a>
                
                <a href="/admin/upload" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Upload IPs
                </a>
                
                <a href="/admin/search" class="nav-link active flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search/Filter
                </a>
            </nav>
            
            <!-- Logout -->
            <div class="px-4 py-4 border-t border-gray-600">
                <button id="logout-btn" class="w-full flex items-center px-4 py-2 text-red-400 rounded-lg hover:bg-red-900/20 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Logout
                </button>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="ml-64 p-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-dark-text">Search & Filter</h1>
            <p class="text-dark-text/70 mt-2">Query and filter IP records in the database</p>
        </div>
        
        <!-- Search Form -->
        <div class="bg-dark-card rounded-lg p-6 border border-gray-600 mb-8">
            <h2 class="text-xl font-semibold text-dark-text mb-4">Filters</h2>
            
            <form id="filter-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-dark-text mb-2">Column</label>
                        <select id="filter-column" class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent">
                            <option value="country">Country</option>
                            <option value="owner">Owner</option>
                            <option value="ip_cidr">IP/CIDR</option>
                            <option value="added_at">Added Date</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-dark-text mb-2">Operator</label>
                        <select id="filter-operator" class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent">
                            <option value="equals">Equals</option>
                            <option value="contains">Contains</option>
                            <option value="gte">Greater than or equal</option>
                            <option value="lte">Less than or equal</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-dark-text mb-2">Value</label>
                        <input 
                            type="text" 
                            id="filter-value" 
                            placeholder="Enter filter value"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        >
                    </div>
                    
                    <div class="flex items-end">
                        <button 
                            type="submit"
                            class="w-full bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200"
                        >
                            Add Filter
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- Active Filters -->
            <div id="active-filters" class="mt-4 hidden">
                <h3 class="text-sm font-medium text-dark-text mb-2">Active Filters</h3>
                <div id="filter-pills" class="flex flex-wrap gap-2">
                    <!-- Filter pills will be added here -->
                </div>
            </div>
            
            <!-- Search Controls -->
            <div class="mt-6 flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <button 
                        id="search-btn"
                        class="bg-dark-secondary hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-md transition-colors duration-200 flex items-center"
                    >
                        <span>Search</span>
                        <div class="spinner ml-2" style="display: none;"></div>
                    </button>
                    
                    <button 
                        id="clear-filters-btn"
                        class="bg-gray-600 hover:bg-gray-500 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200"
                    >
                        Clear All
                    </button>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-dark-text">Results per page:</label>
                    <select id="page-size" class="px-2 py-1 bg-dark-input border border-gray-600 rounded text-dark-text">
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Results Section -->
        <div id="results-section" class="hidden">
            <div class="bg-dark-card rounded-lg border border-gray-600">
                <!-- Results Header -->
                <div class="p-6 border-b border-gray-600">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-dark-text">Search Results</h2>
                        <div class="text-sm text-dark-text/70">
                            <span id="results-info">0 results</span>
                        </div>
                    </div>
                </div>
                
                <!-- Results Table -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-dark-input">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">CIDR</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">Version</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">Country</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">Owner</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">Added</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-dark-text uppercase tracking-wider">Comment</th>
                            </tr>
                        </thead>
                        <tbody id="results-tbody" class="divide-y divide-gray-600">
                            <!-- Results will be populated here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div id="pagination" class="px-6 py-4 border-t border-gray-600 flex justify-between items-center">
                    <div class="text-sm text-dark-text/70">
                        <span id="pagination-info">Showing 0 to 0 of 0 results</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="prev-page" class="px-3 py-1 bg-dark-input text-dark-text rounded hover:bg-gray-600 transition-colors duration-200" disabled>
                            Previous
                        </button>
                        <button id="next-page" class="px-3 py-1 bg-dark-input text-dark-text rounded hover:bg-gray-600 transition-colors duration-200" disabled>
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- No Results -->
        <div id="no-results" class="hidden bg-dark-card rounded-lg p-8 border border-gray-600 text-center">
            <p class="text-dark-text/70">No matching records found</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return;
    }
    
    let currentFilters = [];
    let currentPage = 0;
    let pageSize = 25;
    let totalResults = 0;
    
    // Event listeners
    document.getElementById('logout-btn').addEventListener('click', logout);
    document.getElementById('export-link').addEventListener('click', handleExport);
    document.getElementById('filter-form').addEventListener('submit', addFilter);
    document.getElementById('search-btn').addEventListener('click', performSearch);
    document.getElementById('clear-filters-btn').addEventListener('click', clearAllFilters);
    document.getElementById('page-size').addEventListener('change', function(e) {
        pageSize = parseInt(e.target.value);
        currentPage = 0;
        performSearch();
    });
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 0) {
            currentPage--;
            performSearch();
        }
    });
    document.getElementById('next-page').addEventListener('click', () => {
        const maxPage = Math.ceil(totalResults / pageSize) - 1;
        if (currentPage < maxPage) {
            currentPage++;
            performSearch();
        }
    });
    
    function addFilter(e) {
        e.preventDefault();
        
        const column = document.getElementById('filter-column').value;
        const operator = document.getElementById('filter-operator').value;
        const value = document.getElementById('filter-value').value.trim();
        
        if (!value) {
            showToast('Please enter a filter value', 'error');
            return;
        }
        
        // Check if filter already exists
        const existingFilter = currentFilters.find(f => 
            f.column === column && f.operator === operator && f.value === value
        );
        
        if (existingFilter) {
            showToast('Filter already exists', 'error');
            return;
        }
        
        // Add filter
        currentFilters.push({ column, operator, value });
        
        // Clear form
        document.getElementById('filter-value').value = '';
        
        // Update UI
        updateFilterPills();
        currentPage = 0;
    }
    
    function removeFilter(index) {
        currentFilters.splice(index, 1);
        updateFilterPills();
        currentPage = 0;
    }
    
    function clearAllFilters() {
        currentFilters = [];
        updateFilterPills();
        hideResults();
        currentPage = 0;
    }
    
    function updateFilterPills() {
        const container = document.getElementById('filter-pills');
        const activeFiltersDiv = document.getElementById('active-filters');
        
        if (currentFilters.length === 0) {
            activeFiltersDiv.classList.add('hidden');
            return;
        }
        
        activeFiltersDiv.classList.remove('hidden');
        
        container.innerHTML = '';
        currentFilters.forEach((filter, index) => {
            const pill = document.createElement('div');
            pill.className = 'bg-dark-accent text-white px-3 py-1 rounded-full text-sm flex items-center space-x-2';
            pill.innerHTML = `
                <span>${filter.column} ${filter.operator} "${filter.value}"</span>
                <button onclick="removeFilter(${index})" class="text-white hover:text-red-200">×</button>
            `;
            container.appendChild(pill);
        });
    }
    
    // Make removeFilter available globally
    window.removeFilter = removeFilter;
    
    async function performSearch() {
        const searchBtn = document.getElementById('search-btn');
        setLoading(searchBtn, true);
        
        try {
            const requestData = {
                filters: currentFilters,
                pagination: {
                    skip: currentPage * pageSize,
                    limit: pageSize
                },
                sort: {
                    column: 'added_at',
                    order: 'desc'
                }
            };
            
            const data = await apiCall('/api/v1/admin/query/', {
                method: 'POST',
                body: JSON.stringify(requestData)
            });
            
            totalResults = data.total_matches;
            displayResults(data);
            
        } catch (error) {
            console.error('Search failed:', error);
            showToast(error.message || 'Search failed', 'error');
            hideResults();
        } finally {
            setLoading(searchBtn, false);
        }
    }
    
    function displayResults(data) {
        if (data.total_matches === 0) {
            showNoResults();
            return;
        }
        
        const tbody = document.getElementById('results-tbody');
        tbody.innerHTML = '';
        
        data.data.forEach(record => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-dark-input transition-colors duration-200';
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark-text">${escapeHtml(record.ip_cidr)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark-text">IPv${record.ip_version}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark-text">${escapeHtml(record.country)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark-text">${escapeHtml(record.owner)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark-text/70">${formatDate(record.added_at)}</td>
                <td class="px-6 py-4 text-sm text-dark-text/70 max-w-xs truncate">${escapeHtml(record.comment || '')}</td>
            `;
            
            tbody.appendChild(row);
        });
        
        updatePagination(data);
        showResults();
    }
    
    function updatePagination(data) {
        const start = currentPage * pageSize + 1;
        const end = Math.min(start + data.returned - 1, data.total_matches);
        
        document.getElementById('results-info').textContent = `${data.total_matches} results`;
        document.getElementById('pagination-info').textContent = `Showing ${start} to ${end} of ${data.total_matches} results`;
        
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        
        prevBtn.disabled = currentPage === 0;
        nextBtn.disabled = end >= data.total_matches;
        
        prevBtn.className = `px-3 py-1 bg-dark-input text-dark-text rounded transition-colors duration-200 ${
            prevBtn.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-600'
        }`;
        
        nextBtn.className = `px-3 py-1 bg-dark-input text-dark-text rounded transition-colors duration-200 ${
            nextBtn.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-600'
        }`;
    }
    
    function showResults() {
        document.getElementById('results-section').classList.remove('hidden');
        document.getElementById('no-results').classList.add('hidden');
    }
    
    function showNoResults() {
        document.getElementById('results-section').classList.add('hidden');
        document.getElementById('no-results').classList.remove('hidden');
    }
    
    function hideResults() {
        document.getElementById('results-section').classList.add('hidden');
        document.getElementById('no-results').classList.add('hidden');
    }
    
    async function handleExport(e) {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/v1/admin/export/', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            if (!response.ok) {
                throw new Error('Export failed');
            }
            
            // Create download link
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ip_records_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showToast('Export completed successfully', 'success');
            
        } catch (error) {
            console.error('Export failed:', error);
            showToast('Export failed', 'error');
        }
    }
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Upload IPs - Admin Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-dark-bg">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 w-64 bg-dark-card shadow-lg">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-dark-bg">
                <h1 class="text-xl font-bold text-dark-accent">IP-Check Admin</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2">
                <a href="/admin" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="#" id="export-link" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export IPs
                </a>
                
                <a href="/admin/upload" class="nav-link active flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Upload IPs
                </a>
                
                <a href="/admin/search" class="nav-link flex items-center px-4 py-2 text-dark-text rounded-lg hover:bg-dark-input transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search/Filter
                </a>
            </nav>
            
            <!-- Logout -->
            <div class="px-4 py-4 border-t border-gray-600">
                <button id="logout-btn" class="w-full flex items-center px-4 py-2 text-red-400 rounded-lg hover:bg-red-900/20 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Logout
                </button>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="ml-64 p-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-dark-text">Upload IP Records</h1>
            <p class="text-dark-text/70 mt-2">Add new IP entries to the database</p>
        </div>
        
        <!-- Upload Forms -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Single Record Upload -->
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <h2 class="text-xl font-semibold text-dark-text mb-4">Single Record</h2>
                <form id="single-upload-form" class="space-y-4">
                    <div>
                        <label for="single-ip" class="block text-sm font-medium text-dark-text mb-2">
                            IP or CIDR *
                        </label>
                        <input 
                            type="text" 
                            id="single-ip" 
                            name="ip"
                            placeholder="e.g., ***********/24 or *******"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                            required
                        >
                    </div>
                    
                    <div>
                        <label for="single-country" class="block text-sm font-medium text-dark-text mb-2">
                            Country *
                        </label>
                        <input 
                            type="text" 
                            id="single-country" 
                            name="country"
                            placeholder="e.g., United States"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                            required
                        >
                    </div>
                    
                    <div>
                        <label for="single-owner" class="block text-sm font-medium text-dark-text mb-2">
                            Owner *
                        </label>
                        <input 
                            type="text" 
                            id="single-owner" 
                            name="owner"
                            placeholder="e.g., Google LLC"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                            required
                        >
                    </div>
                    
                    <div>
                        <label for="single-comment" class="block text-sm font-medium text-dark-text mb-2">
                            Comment
                        </label>
                        <textarea 
                            id="single-comment" 
                            name="comment"
                            rows="3"
                            placeholder="Optional comment or notes"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        ></textarea>
                    </div>
                    
                    <button 
                        type="submit" 
                        id="single-upload-btn"
                        class="w-full bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                    >
                        <span>Upload Single Record</span>
                        <div class="spinner ml-2" style="display: none;"></div>
                    </button>
                </form>
            </div>
            
            <!-- Bulk Upload -->
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <h2 class="text-xl font-semibold text-dark-text mb-4">Bulk Upload</h2>
                <form id="bulk-upload-form" class="space-y-4">
                    <div>
                        <label for="bulk-file" class="block text-sm font-medium text-dark-text mb-2">
                            CSV/Text File *
                        </label>
                        <input 
                            type="file" 
                            id="bulk-file" 
                            name="file"
                            accept=".csv,.txt"
                            class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-dark-accent file:text-white hover:file:bg-dark-secondary"
                            required
                        >
                        <p class="text-xs text-dark-text/70 mt-1">
                            Max file size: 10MB. Formats: .csv, .txt
                        </p>
                    </div>
                    
                    <div class="bg-dark-input rounded-md p-4">
                        <h3 class="text-sm font-medium text-dark-text mb-2">File Format</h3>
                        <p class="text-xs text-dark-text/70 mb-2">
                            Each line should contain comma-separated values in this order:
                        </p>
                        <code class="text-xs text-dark-accent">ip_cidr,country,owner,comment</code>
                        <p class="text-xs text-dark-text/70 mt-2">
                            Example:<br>
                            <code class="text-dark-accent">***********/24,United States,Example Corp,Corporate network</code>
                        </p>
                    </div>
                    
                    <button 
                        type="submit" 
                        id="bulk-upload-btn"
                        class="w-full bg-dark-secondary hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                    >
                        <span>Upload Bulk File</span>
                        <div class="spinner ml-2" style="display: none;"></div>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Upload Results -->
        <div id="upload-results" class="hidden mt-8">
            <div class="bg-dark-card rounded-lg p-6 border border-gray-600">
                <h2 class="text-xl font-semibold text-dark-text mb-4">Upload Results</h2>
                <div id="results-content">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return;
    }
    
    // Event listeners
    document.getElementById('logout-btn').addEventListener('click', logout);
    document.getElementById('export-link').addEventListener('click', handleExport);
    document.getElementById('single-upload-form').addEventListener('submit', handleSingleUpload);
    document.getElementById('bulk-upload-form').addEventListener('submit', handleBulkUpload);
    
    async function handleSingleUpload(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const button = document.getElementById('single-upload-btn');
        
        setLoading(button, true);
        
        try {
            const response = await fetch('/api/v1/admin/upload/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: formData
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || 'Upload failed');
            }
            
            showUploadResults(data);
            form.reset();
            showToast('Single record uploaded successfully', 'success');
            
        } catch (error) {
            console.error('Single upload failed:', error);
            showToast(error.message || 'Upload failed', 'error');
        } finally {
            setLoading(button, false);
        }
    }
    
    async function handleBulkUpload(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const button = document.getElementById('bulk-upload-btn');
        
        setLoading(button, true);
        
        try {
            const response = await fetch('/api/v1/admin/upload/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: formData
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                if (data.detail && typeof data.detail === 'object') {
                    // Handle bulk upload failure with errors
                    showUploadResults(data.detail);
                    showToast(data.detail.message || 'Bulk upload failed', 'error');
                } else {
                    throw new Error(data.detail || 'Upload failed');
                }
            } else {
                showUploadResults(data);
                form.reset();
                showToast(`Bulk upload completed: ${data.inserted} records inserted`, 'success');
            }
            
        } catch (error) {
            console.error('Bulk upload failed:', error);
            showToast(error.message || 'Upload failed', 'error');
        } finally {
            setLoading(button, false);
        }
    }
    
    function showUploadResults(data) {
        const resultsDiv = document.getElementById('upload-results');
        const contentDiv = document.getElementById('results-content');
        
        let html = '<div class="space-y-4">';
        
        // Summary
        html += '<div class="grid grid-cols-2 md:grid-cols-4 gap-4">';
        html += `<div class="text-center"><div class="text-2xl font-bold text-green-400">${data.inserted || 0}</div><div class="text-sm text-dark-text/70">Inserted</div></div>`;
        html += `<div class="text-center"><div class="text-2xl font-bold text-yellow-400">${data.skipped_conflicts || 0}</div><div class="text-sm text-dark-text/70">Conflicts</div></div>`;
        html += `<div class="text-center"><div class="text-2xl font-bold text-red-400">${data.skipped_invalid || 0}</div><div class="text-sm text-dark-text/70">Invalid</div></div>`;
        html += `<div class="text-center"><div class="text-2xl font-bold text-blue-400">${data.skipped_duplicates || 0}</div><div class="text-sm text-dark-text/70">Duplicates</div></div>`;
        html += '</div>';
        
        // Errors
        if (data.errors && data.errors.length > 0) {
            html += '<div class="mt-6">';
            html += '<h3 class="text-lg font-semibold text-dark-text mb-3">Errors</h3>';
            html += '<div class="bg-red-900/20 border border-red-500 rounded-md p-4 max-h-64 overflow-y-auto">';
            
            data.errors.slice(0, 50).forEach(error => {
                html += `<div class="text-sm text-red-200 mb-1">Line ${error.line}: ${escapeHtml(error.reason)}</div>`;
            });
            
            if (data.errors.length > 50) {
                html += `<div class="text-sm text-red-300 mt-2">... and ${data.errors.length - 50} more errors</div>`;
            }
            
            html += '</div>';
            html += '</div>';
        }
        
        html += '</div>';
        
        contentDiv.innerHTML = html;
        resultsDiv.classList.remove('hidden');
    }
    
    async function handleExport(e) {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/v1/admin/export/', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            if (!response.ok) {
                throw new Error('Export failed');
            }
            
            // Create download link
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ip_records_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showToast('Export completed successfully', 'success');
            
        } catch (error) {
            console.error('Export failed:', error);
            showToast('Export failed', 'error');
        }
    }
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Admin Login - IP Lookup{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-3xl font-bold text-dark-accent mb-2">IP-Check</h1>
            <h2 class="text-xl font-semibold text-dark-text">Admin Login</h2>
            <p class="text-dark-text/70 mt-2">Sign in to access the admin dashboard</p>
        </div>
        
        <!-- Login Form -->
        <div class="bg-dark-card rounded-lg shadow-xl p-8">
            <form id="login-form" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-dark-text mb-2">
                        Username
                    </label>
                    <input 
                        id="username" 
                        name="username" 
                        type="text" 
                        required
                        class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        placeholder="Enter your username"
                    >
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-dark-text mb-2">
                        Password
                    </label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        required
                        class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        placeholder="Enter your password"
                    >
                </div>
                
                <!-- Error Message -->
                <div id="error-message" class="hidden bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md">
                    <p class="text-sm"></p>
                </div>
                
                <button 
                    type="submit" 
                    id="login-btn"
                    class="w-full bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                >
                    <span class="login-text">Sign In</span>
                    <div class="spinner ml-2" style="display: none;"></div>
                </button>
            </form>
        </div>
        
        <!-- Registration Link -->
        <div class="text-center space-y-2">
            <p class="text-dark-text/70">Don't have an account?</p>
            <a href="/register" class="text-dark-accent hover:text-dark-secondary transition-colors duration-200 font-medium">
                Create Admin Account
            </a>
            <div class="mt-4">
                <a href="/" class="text-dark-text/70 hover:text-dark-accent transition-colors duration-200">
                    ← Back to IP Lookup
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Redirect if already logged in
    if (isAuthenticated()) {
        window.location.href = '/admin';
        return;
    }
    
    const form = document.getElementById('login-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('login-btn');
    const errorMessage = document.getElementById('error-message');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        
        if (!username || !password) {
            showError('Please enter both username and password');
            return;
        }
        
        // Show loading state
        setLoading(loginBtn, true);
        hideError();
        
        try {
            const data = await apiCall('/api/v1/auth/login/', {
                method: 'POST',
                body: JSON.stringify({
                    username: username,
                    password: password
                }),
                skipAuth: true
            });
            
            // Store token
            localStorage.setItem('access_token', data.access_token);
            
            // Show success message and redirect
            showToast('Login successful! Redirecting...', 'success');
            
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1000);
            
        } catch (error) {
            let errorMsg = 'Login failed';
            
            if (error.message.includes('Invalid username or password')) {
                errorMsg = 'Invalid username or password';
            } else if (error.message.includes('locked')) {
                errorMsg = 'Account is temporarily locked due to too many failed attempts';
            } else if (error.message) {
                errorMsg = error.message;
            }
            
            showError(errorMsg);
        } finally {
            setLoading(loginBtn, false);
        }
    });
    
    function showError(message) {
        const errorP = errorMessage.querySelector('p');
        errorP.textContent = message;
        errorMessage.classList.remove('hidden');
    }
    
    function hideError() {
        errorMessage.classList.add('hidden');
    }
    
    // Clear error when user starts typing
    usernameInput.addEventListener('input', hideError);
    passwordInput.addEventListener('input', hideError);
});
</script>
{% endblock %}

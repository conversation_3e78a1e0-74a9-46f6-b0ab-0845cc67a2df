{% extends "base.html" %}

{% block title %}Register - IP Lookup{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-3xl font-bold text-dark-accent mb-2">IP-Check</h1>
            <h2 class="text-xl font-semibold text-dark-text">Create Admin Account</h2>
            <p class="text-dark-text/70 mt-2">Sign up to access the admin dashboard</p>
        </div>
        
        <!-- Registration Form -->
        <div class="bg-dark-card rounded-lg shadow-xl p-8">
            <form id="register-form" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-dark-text mb-2">
                        Username *
                    </label>
                    <input 
                        id="username" 
                        name="username" 
                        type="text" 
                        required
                        minlength="4"
                        maxlength="50"
                        class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        placeholder="Enter username (4-50 characters)"
                    >
                    <p class="text-xs text-dark-text/70 mt-1">Username must be 4-50 characters long</p>
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-dark-text mb-2">
                        Password *
                    </label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        required
                        minlength="8"
                        class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        placeholder="Enter password"
                    >
                    <div class="mt-2 text-xs text-dark-text/70">
                        <p>Password must include:</p>
                        <ul class="list-disc list-inside ml-2 space-y-1">
                            <li id="req-length" class="text-red-400">At least 8 characters</li>
                            <li id="req-upper" class="text-red-400">One uppercase letter</li>
                            <li id="req-lower" class="text-red-400">One lowercase letter</li>
                            <li id="req-digit" class="text-red-400">One digit</li>
                            <li id="req-symbol" class="text-red-400">One special character</li>
                        </ul>
                    </div>
                </div>
                
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-dark-text mb-2">
                        Confirm Password *
                    </label>
                    <input 
                        id="confirm_password" 
                        name="confirm_password" 
                        type="password" 
                        required
                        minlength="8"
                        class="w-full px-3 py-2 bg-dark-input border border-gray-600 rounded-md text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent"
                        placeholder="Confirm password"
                    >
                    <p id="password-match" class="text-xs mt-1 hidden"></p>
                </div>
                
                <!-- Error Message -->
                <div id="error-message" class="hidden bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md">
                    <p class="text-sm"></p>
                </div>
                
                <!-- Success Message -->
                <div id="success-message" class="hidden bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-md">
                    <p class="text-sm"></p>
                </div>
                
                <button 
                    type="submit" 
                    id="register-btn"
                    class="w-full bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                >
                    <span class="register-text">Create Account</span>
                    <div class="spinner ml-2" style="display: none;"></div>
                </button>
            </form>
        </div>
        
        <!-- Back to Login -->
        <div class="text-center space-y-2">
            <p class="text-dark-text/70">Already have an account?</p>
            <a href="/login" class="text-dark-accent hover:text-dark-secondary transition-colors duration-200 font-medium">
                Sign In
            </a>
            <div class="mt-4">
                <a href="/" class="text-dark-text/70 hover:text-dark-accent transition-colors duration-200">
                    ← Back to IP Lookup
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('register-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const registerBtn = document.getElementById('register-btn');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');
    
    // Password requirements elements
    const reqLength = document.getElementById('req-length');
    const reqUpper = document.getElementById('req-upper');
    const reqLower = document.getElementById('req-lower');
    const reqDigit = document.getElementById('req-digit');
    const reqSymbol = document.getElementById('req-symbol');
    const passwordMatch = document.getElementById('password-match');
    
    let passwordValid = false;
    let passwordsMatch = false;
    let usernameValid = false;
    
    // Real-time password validation
    passwordInput.addEventListener('input', function() {
        const password = passwordInput.value;
        
        // Check length
        if (password.length >= 8) {
            reqLength.className = 'text-green-400';
        } else {
            reqLength.className = 'text-red-400';
        }
        
        // Check uppercase
        if (/[A-Z]/.test(password)) {
            reqUpper.className = 'text-green-400';
        } else {
            reqUpper.className = 'text-red-400';
        }
        
        // Check lowercase
        if (/[a-z]/.test(password)) {
            reqLower.className = 'text-green-400';
        } else {
            reqLower.className = 'text-red-400';
        }
        
        // Check digit
        if (/\d/.test(password)) {
            reqDigit.className = 'text-green-400';
        } else {
            reqDigit.className = 'text-red-400';
        }
        
        // Check symbol
        if (/[^a-zA-Z0-9]/.test(password)) {
            reqSymbol.className = 'text-green-400';
        } else {
            reqSymbol.className = 'text-red-400';
        }
        
        // Update password validity
        passwordValid = password.length >= 8 && 
                       /[A-Z]/.test(password) && 
                       /[a-z]/.test(password) && 
                       /\d/.test(password) && 
                       /[^a-zA-Z0-9]/.test(password);
        
        // Check password match if confirm password has value
        if (confirmPasswordInput.value) {
            checkPasswordMatch();
        }
        
        updateSubmitButton();
    });
    
    // Real-time password confirmation validation
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (confirmPassword) {
            if (password === confirmPassword) {
                passwordMatch.textContent = 'Passwords match ✓';
                passwordMatch.className = 'text-xs mt-1 text-green-400';
                passwordMatch.classList.remove('hidden');
                passwordsMatch = true;
            } else {
                passwordMatch.textContent = 'Passwords do not match';
                passwordMatch.className = 'text-xs mt-1 text-red-400';
                passwordMatch.classList.remove('hidden');
                passwordsMatch = false;
            }
        } else {
            passwordMatch.classList.add('hidden');
            passwordsMatch = false;
        }
        
        updateSubmitButton();
    }
    
    // Username validation
    usernameInput.addEventListener('input', function() {
        const username = usernameInput.value.trim();
        usernameValid = username.length >= 4 && username.length <= 50;
        updateSubmitButton();
    });
    
    function updateSubmitButton() {
        registerBtn.disabled = !(usernameValid && passwordValid && passwordsMatch);
    }
    
    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (!usernameValid || !passwordValid || !passwordsMatch) {
            showError('Please fix the validation errors above');
            return;
        }
        
        // Show loading state
        setLoading(registerBtn, true);
        hideMessages();
        
        try {
            const data = await apiCall('/api/v1/auth/register/', {
                method: 'POST',
                body: JSON.stringify({
                    username: username,
                    password: password,
                    confirm_password: confirmPassword
                }),
                skipAuth: true
            });
            
            // Show success message
            showSuccess(data.message);
            
            // Clear form
            form.reset();
            updatePasswordRequirements();
            passwordMatch.classList.add('hidden');
            updateSubmitButton();
            
            // Redirect to login after 1 second
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
            
        } catch (error) {
            let errorMsg = 'Registration failed';
            
            if (error.message.includes('Username already exists')) {
                errorMsg = 'Username already exists. Please choose a different username.';
            } else if (error.message.includes('Passwords do not match')) {
                errorMsg = 'Passwords do not match';
            } else if (error.message.includes('Password must be')) {
                errorMsg = 'Password does not meet requirements';
            } else if (error.message) {
                errorMsg = error.message;
            }
            
            showError(errorMsg);
        } finally {
            setLoading(registerBtn, false);
        }
    });
    
    function updatePasswordRequirements() {
        // Reset all requirements to red
        reqLength.className = 'text-red-400';
        reqUpper.className = 'text-red-400';
        reqLower.className = 'text-red-400';
        reqDigit.className = 'text-red-400';
        reqSymbol.className = 'text-red-400';
        passwordValid = false;
        passwordsMatch = false;
    }
    
    function showError(message) {
        const errorP = errorMessage.querySelector('p');
        errorP.textContent = message;
        errorMessage.classList.remove('hidden');
        successMessage.classList.add('hidden');
    }
    
    function showSuccess(message) {
        const successP = successMessage.querySelector('p');
        successP.textContent = message;
        successMessage.classList.remove('hidden');
        errorMessage.classList.add('hidden');
    }
    
    function hideMessages() {
        errorMessage.classList.add('hidden');
        successMessage.classList.add('hidden');
    }
    
    // Clear messages when user starts typing
    usernameInput.addEventListener('input', hideMessages);
    passwordInput.addEventListener('input', hideMessages);
    confirmPasswordInput.addEventListener('input', hideMessages);
});
</script>
{% endblock %}

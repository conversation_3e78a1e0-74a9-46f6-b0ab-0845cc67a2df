# IP Lookup Application - Logging System

This directory contains comprehensive logs for the IP Lookup web application. The logging system provides detailed tracking of all application activities, security events, and system operations.

## Log Files Overview

### 📊 **app_activity.log**
**Purpose**: General application events and lifecycle
**Contains**:
- Application startup/shutdown events
- System initialization
- General application state changes
- Configuration loading events

**Example**:
```
2025-06-01 13:00:00 | app | INFO | lifespan:57 | Starting IP Lookup application...
2025-06-01 13:00:00 | app | INFO | log_application_event:245 | App Event: Application startup initiated
```

### 🔒 **security.log**
**Purpose**: Security-related events and authentication activities
**Contains**:
- Login attempts (successful/failed)
- User registration events
- Account lockouts
- Rate limiting violations
- Authentication failures
- Authorization events

**Example**:
```
2025-06-01 13:01:25 | security | INFO | log_security_event:174 | Security Event: Login attempt successful | IP: 127.0.0.1
2025-06-01 13:01:30 | security | INFO | log_security_event:174 | Security Event: User registration successful | IP: 127.0.0.1
```

### 🌐 **api_access.log**
**Purpose**: HTTP request and response tracking
**Contains**:
- All HTTP requests with method, URL, status code
- Response times
- Client IP addresses
- User agent information
- Request processing metrics

**Example**:
```
2025-06-01 13:00:18 | INFO | 127.0.0.1 | GET http://localhost:8000/ | Status: 200 | Time: 0.005s | UA: curl/8.11.0
```

### 💾 **database.log**
**Purpose**: Database operations and queries
**Contains**:
- Database connection events
- Table creation/modification
- Query execution details
- Transaction operations
- Database performance metrics

**Example**:
```
2025-06-01 13:00:00 | database | INFO | create_db_and_tables:45 | Database tables and indexes created successfully
```

### ❌ **errors.log**
**Purpose**: Application errors and exceptions
**Contains**:
- Unhandled exceptions
- Error stack traces
- System failures
- Critical application errors
- Error context and user information

**Example**:
```
2025-06-01 13:00:00 | error | ERROR | log_error:267 | Error: ValueError: Invalid IP format | Context: IP lookup validation
```

### 👨‍💼 **admin_activity.log**
**Purpose**: Administrative actions and operations
**Contains**:
- Admin user actions
- IP record uploads (single/bulk)
- Data exports
- Search/filter operations
- Administrative configuration changes

**Example**:
```
2025-06-01 13:02:00 | admin | INFO | log_admin_action:253 | Admin Action: bulk_upload | User: admin | IP: 127.0.0.1 | Details: {'filename': 'sample_data.csv', 'inserted': 10}
```

## Log Rotation and Management

### **Automatic Rotation**
- **File Size Limit**: 10MB per log file (20MB for API access logs)
- **Backup Count**: 5-10 backup files retained
- **Rotation Method**: Size-based rotation with timestamp preservation

### **Log Retention**
- **Security Logs**: 10 backup files (extended retention for compliance)
- **API Access Logs**: 7 backup files (1 week of detailed access logs)
- **Application Logs**: 5 backup files (standard retention)
- **Error Logs**: 10 backup files (extended retention for debugging)

### **File Naming Convention**
- **Active Log**: `logname.log`
- **Rotated Logs**: `logname.log.1`, `logname.log.2`, etc.
- **Oldest logs are automatically deleted when backup count is exceeded**

## Log Levels

### **INFO Level**
- Normal application operations
- Successful requests and responses
- User actions and system events
- Performance metrics

### **WARNING Level**
- Non-critical issues
- Deprecated feature usage
- Performance degradation
- Configuration warnings

### **ERROR Level**
- Application errors
- Failed requests
- System failures
- Critical issues requiring attention

## Monitoring and Analysis

### **Key Metrics to Monitor**
1. **Security Events**: Failed login attempts, rate limiting
2. **Performance**: Response times, database query performance
3. **Errors**: Error rates, exception patterns
4. **Usage**: API endpoint usage, admin activity patterns

### **Log Analysis Tools**
- **grep**: Search for specific patterns
- **tail -f**: Real-time log monitoring
- **awk/sed**: Log parsing and analysis
- **Log aggregation tools**: ELK Stack, Splunk, etc.

### **Common Analysis Commands**
```bash
# Monitor real-time activity
tail -f logs/api_access.log

# Search for failed login attempts
grep "Login attempt failed" logs/security.log

# Count requests by IP
grep "127.0.0.1" logs/api_access.log | wc -l

# Find errors in the last hour
grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" logs/errors.log
```

## Security Considerations

### **Log Protection**
- Logs contain sensitive information (IP addresses, usernames)
- Ensure proper file permissions (600 or 640)
- Regular backup to secure storage
- Consider log encryption for sensitive environments

### **Privacy Compliance**
- IP addresses are logged for security purposes
- User actions are tracked for audit compliance
- Consider data retention policies based on local regulations
- Implement log anonymization if required

## Troubleshooting

### **Common Issues**
1. **Log files not created**: Check directory permissions
2. **Logs not rotating**: Verify disk space and file permissions
3. **Missing log entries**: Check log level configuration
4. **Performance impact**: Monitor log file sizes and rotation

### **Log Configuration**
- Configuration file: `logging_config.py`
- Modify log levels, file sizes, and retention as needed
- Restart application after configuration changes

## Integration with Monitoring Systems

### **Log Forwarding**
The logging system is designed to integrate with:
- **Syslog**: Forward logs to centralized syslog server
- **ELK Stack**: Elasticsearch, Logstash, Kibana for analysis
- **Prometheus**: Metrics extraction from logs
- **Grafana**: Visualization and alerting

### **Alerting**
Set up alerts for:
- High error rates
- Security events (multiple failed logins)
- Performance degradation
- System failures

---

**Note**: This logging system provides comprehensive visibility into the IP Lookup application's operations. Regular monitoring and analysis of these logs will help maintain security, performance, and reliability.

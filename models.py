from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel
import ipaddress


class IPRecord(SQLModel, table=True):
    """Database model for IP records with range storage for efficient lookups."""
    __tablename__ = "ip_records"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    ip_cidr: str = Field(index=True, nullable=False, max_length=50)
    ip_version: int = Field(nullable=False)
    ip_start: int = Field(nullable=False, index=True)
    ip_end: int = Field(nullable=False, index=True)
    country: str = Field(nullable=False, index=True, max_length=100)
    owner: str = Field(nullable=False, index=True, max_length=255)
    added_at: datetime = Field(default_factory=datetime.utcnow, nullable=False, index=True)
    comment: Optional[str] = Field(default=None, max_length=500)

    @classmethod
    def from_cidr(cls, cidr_str: str, country: str, owner: str, comment: Optional[str] = None):
        """Create IPRecord from CIDR string with automatic range calculation."""
        try:
            # Handle both single IPs and CIDR notation
            if '/' not in cidr_str:
                # Single IP - treat as /32 for IPv4 or /128 for IPv6
                ip = ipaddress.ip_address(cidr_str)
                if ip.version == 4:
                    network = ipaddress.ip_network(f"{cidr_str}/32", strict=False)
                else:
                    network = ipaddress.ip_network(f"{cidr_str}/128", strict=False)
            else:
                network = ipaddress.ip_network(cidr_str, strict=False)
            
            return cls(
                ip_cidr=str(network),
                ip_version=network.version,
                ip_start=int(network.network_address),
                ip_end=int(network.broadcast_address),
                country=country.strip(),
                owner=owner.strip(),
                comment=comment.strip() if comment else None,
            )
        except (ipaddress.AddressValueError, ValueError) as e:
            raise ValueError(f"Invalid IP/CIDR format: {cidr_str}") from e


class AdminUser(SQLModel, table=True):
    """Database model for admin users with authentication data."""
    __tablename__ = "admin_users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(nullable=False, unique=True, max_length=50, min_length=4)
    hashed_pwd: str = Field(nullable=False)
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_login_at: Optional[datetime] = None


class AuditLog(SQLModel, table=True):
    """Database model for audit logging of admin actions."""
    __tablename__ = "audit_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    timestamp: datetime = Field(default_factory=datetime.utcnow, nullable=False, index=True)
    admin_id: int = Field(nullable=False, index=True)
    action_type: str = Field(nullable=False, max_length=50)  # e.g. "bulk_upload", "single_upload", "export", "query"
    detail_json: str = Field(nullable=False)  # JSON string with details


class LoginAttempt(SQLModel, table=True):
    """Database model for tracking failed login attempts for lockout functionality."""
    __tablename__ = "login_attempts"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(nullable=False, index=True, max_length=50)
    ip_address: str = Field(nullable=False, max_length=45)  # IPv6 max length
    timestamp: datetime = Field(default_factory=datetime.utcnow, nullable=False, index=True)
    success: bool = Field(default=False)


# Pydantic models for API requests/responses
class IPLookupRequest(SQLModel):
    """Request model for IP lookup endpoint."""
    query: str = Field(..., min_length=1, max_length=50, description="IPv4, IPv6, or CIDR to lookup")


class IPLookupResponse(SQLModel):
    """Response model for IP lookup results."""
    ip_cidr: str
    ip_version: int
    country: str
    owner: str
    added_at: datetime
    comment: Optional[str] = None


class LoginRequest(SQLModel):
    """Request model for admin login."""
    username: str = Field(..., min_length=4, max_length=50)
    password: str = Field(..., min_length=8)


class LoginResponse(SQLModel):
    """Response model for successful login."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 900  # 15 minutes


class RegisterRequest(SQLModel):
    """Request model for user registration."""
    username: str = Field(..., min_length=4, max_length=50, description="Username (4-50 characters)")
    password: str = Field(..., min_length=8, description="Password (min 8 chars, must include uppercase, lowercase, digit, symbol)")
    confirm_password: str = Field(..., min_length=8, description="Password confirmation")


class RegisterResponse(SQLModel):
    """Response model for successful registration."""
    message: str
    username: str


class SingleUploadRequest(SQLModel):
    """Request model for single IP record upload."""
    ip: str = Field(..., description="IPv4, IPv6, or CIDR")
    country: str = Field(..., min_length=1, max_length=100)
    owner: str = Field(..., min_length=1, max_length=255)
    comment: Optional[str] = Field(None, max_length=500)


class FilterRequest(SQLModel):
    """Request model for admin query filtering."""
    column: str = Field(..., description="Column to filter on")
    operator: str = Field(..., description="Filter operator")
    value: str = Field(..., description="Filter value")


class QueryRequest(SQLModel):
    """Request model for admin query endpoint."""
    filters: list[FilterRequest] = Field(default_factory=list)
    pagination: dict = Field(default={"skip": 0, "limit": 100})
    sort: dict = Field(default={"column": "added_at", "order": "desc"})


class QueryResponse(SQLModel):
    """Response model for admin query results."""
    total_matches: int
    returned: int
    data: list[IPLookupResponse]


class UploadResponse(SQLModel):
    """Response model for upload operations."""
    inserted: int
    skipped_conflicts: int = 0
    skipped_invalid: int = 0
    skipped_duplicates: int = 0
    errors: list[dict] = Field(default_factory=list)

import ipaddress
import logging
from typing import List
from fastapi import APIRouter, HTTPException, status
from models import IPLookupRequest, IPLookupResponse
from database import execute_query

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/lookup/", response_model=List[IPLookupResponse])
async def lookup_ip(request: IPLookupRequest):
    """
    Public endpoint to lookup IP address or CIDR range.
    
    Accepts IPv4, IPv6, or CIDR notation and returns matching database entries.
    """
    try:
        query_str = request.query.strip()
        
        # Parse the input to determine if it's a single IP or CIDR
        try:
            if '/' in query_str:
                # CIDR notation
                network = ipaddress.ip_network(query_str, strict=False)
                target_start = int(network.network_address)
                target_end = int(network.broadcast_address)
                
                # Find all overlapping ranges
                sql_query = """
                    SELECT ip_cidr, ip_version, country, owner, added_at, comment
                    FROM ip_records 
                    WHERE ip_start <= :target_end 
                    AND ip_end >= :target_start
                    ORDER BY ip_start ASC
                """
                
                results = await execute_query(sql_query, {
                    "target_start": target_start,
                    "target_end": target_end
                })
                
            else:
                # Single IP address
                ip = ipaddress.ip_address(query_str)
                target = int(ip)
                
                # Find containing range
                sql_query = """
                    SELECT ip_cidr, ip_version, country, owner, added_at, comment
                    FROM ip_records 
                    WHERE ip_start <= :target 
                    AND ip_end >= :target
                    LIMIT 1
                """
                
                result = await execute_query(sql_query, {"target": target})
                results = [result[0]] if result else []
                
        except (ipaddress.AddressValueError, ValueError) as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid IP/CIDR format: '{query_str}'"
            )
        
        # Convert results to response models
        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for {query_str}"
            )
        
        response_data = []
        for row in results:
            response_data.append(IPLookupResponse(
                ip_cidr=row["ip_cidr"],
                ip_version=row["ip_version"],
                country=row["country"],
                owner=row["owner"],
                added_at=row["added_at"],
                comment=row["comment"]
            ))
        
        logger.info(f"Lookup successful for {query_str}: {len(response_data)} results")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lookup error for {request.query}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during lookup"
        )
